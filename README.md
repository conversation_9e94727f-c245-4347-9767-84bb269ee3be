# iOS SwiftUI Authentication App

A comprehensive iOS application built with SwiftUI demonstrating Clean Architecture principles with MVVM pattern, advanced theme management, Azure Communication Services integration, and modern iOS development practices.

## 📋 Table of Contents

- [App Purpose](#-app-purpose)
- [Clean Architecture Overview](#️-clean-architecture-overview)
- [Folder Structure](#-folder-structure)
- [Run Instructions](#-run-instructions)
- [Features and Screenshots](#-features-and-screenshots)
- [Azure Communication Services](#-azure-communication-services)
- [What Would Be Added If More Time](#-what-would-be-added-if-more-time)
- [Contributing](#-contributing)
- [Troubleshooting](#-troubleshooting)
- [Changelog](#-changelog)
- [License](#-license)
- [Contact](#-contact)

## 📱 App Purpose

This iOS application is a multi-screen authentication and navigation app that showcases:

- **User Authentication**: Secure login with email/password validation and form validation
- **Theme Management**: Dynamic light/dark mode switching with persistent user preferences
- **Navigation System**: SwiftUI-based navigation with authentication flow and dashboard
- **Modern UI/UX**: SwiftUI components with custom animations and iOS design patterns
- **Dependency Injection**: Comprehensive DI container for scalable architecture
- **Azure Communication Services**: Production-ready video calling and Teams meeting integration

The app serves as a demonstration of enterprise-level iOS development practices using SwiftUI, focusing on scalability, maintainability, and iOS-specific best practices.

## 🏗️ Clean Architecture Overview

This project implements **Clean Architecture** principles with clear separation of concerns across three main layers:

### Architecture Flow
```
SwiftUI Views → ViewModels (MVVM) → Use Cases → Repository Interface → Data Sources (Mock)
```

### Layer Responsibilities

**🎨 Presentation Layer** (`/Presentation/`)
- SwiftUI views and screens
- ViewModels with ObservableObject
- User input handling and validation
- Navigation coordination and routing

**🧠 Domain Layer** (`/Domain/`)
- Business entities and models
- Use cases (application logic)
- Repository protocols (contracts)
- Business rules and validation

**💾 Data Layer** (`/Data/`)
- Repository implementations
- Data sources (mock data for demo)
- Data models and DTOs
- External service integrations

**🔧 Core Layer** (`/Core/`)
- **Dependency Injection**: Complete DI container with service registration and resolution
- **Services**: Navigation, Validation, and Azure Communication Services
- **Constants**: Centralized app constants, dimensions, and image assets
- **Extensions**: Logger system and String utilities
- **Resource Management**: Assets, fonts, localization, and configuration files

## 📁 Folder Structure

```
iOSProject/
├── Core/                          # Infrastructure and core utilities
│   ├── Constants/                 # App constants, dimensions, and image assets
│   │   ├── AppConstants.swift     # Centralized app constants and configurations
│   │   ├── AppDimensions.swift    # Responsive UI dimensions and layout constants
│   │   └── ImageConstants.swift   # Image asset management and safe loading
│   ├── DependencyInjection/       # Complete DI container implementation
│   │   ├── DIContainer.swift      # Service registration and resolution
│   │   └── DependencyContainer.swift # Environment-based dependency access
│   ├── Extensions/                # Utility extensions and logging
│   │   ├── Logger+Extensions.swift # Comprehensive logging system
│   │   └── String+Extension.swift  # String utilities and validation helpers
│   ├── Resource/                  # Assets and configuration files
│   │   ├── ACSConfig.plist        # Azure Communication Services configuration
│   │   ├── Assets.xcassets        # App icons, images, and color assets
│   │   ├── Fonts/                 # Custom font files
│   │   ├── Info.plist             # App configuration and permissions
│   │   └── Localizable.xcstrings  # Localization strings
│   └── Services/                  # Core infrastructure services
│       ├── ACS/                   # Azure Communication Services
│       │   ├── ACSConfiguration.swift # ACS setup and configuration
│       │   ├── ACSImports.swift   # ACS SDK imports and constants
│       │   ├── ACSService.swift   # Production-ready ACS implementation
│       │   └── ACSServiceType.swift # ACS service type definitions
│       ├── Navigation/            # Specialized navigation services
│       │   ├── PageNavigationService.swift # Main app navigation and routing
│       │   ├── SheetNavigationService.swift # Modal presentations and alerts
│       │   └── TabNavigationService.swift # Tab-based navigation management
│       ├── NavigationCoordinator.swift # Unified navigation coordination
│       ├── NavigationService.swift # Legacy navigation service (compatibility)
│       ├── NavigationServiceProtocol.swift # Navigation service contracts
│       ├── ValidationService.swift # Form and input validation
│       ├── AuthNavigationUseCaseImpl.swift # Auth-specific navigation logic
│       └── ValidationUseCaseImpl.swift # Validation use case implementation
├── Data/                         # Data layer implementation
│   ├── DataSources/              # Mock data sources
│   └── Repositories/             # Repository implementations
├── Domain/                       # Business logic layer
│   ├── Common/                   # Common domain types
│   ├── Models/                   # Business entities
│   ├── Repositories/             # Repository protocols
│   └── UseCases/                 # Application use cases
├── Presentation/                 # UI layer
│   ├── App/                      # App entry point
│   ├── Authentication/           # Login/auth UI and ViewModels
│   ├── Common/                   # Reusable UI components
│   ├── Dashboard/                # Main dashboard screens
│   ├── Main/                     # Main app coordinator
│   ├── Profile/                  # User profile screens
│   ├── SuperBase/                # Base view components
│   └── Theme/                    # Theme management
└── Tests/                        # Test files
    ├── iOSProjectTests/          # Unit tests
    └── iOSProjectUITests/        # UI tests
```

## 🔧 Core Layer Implementation Details

The Core layer serves as the infrastructure foundation of the application, containing all cross-cutting concerns and shared utilities. Here's what's actually implemented:

### 🏗️ Dependency Injection System
- **DIContainer.swift**: Complete service registration with singleton and factory patterns
- **DependencyContainer.swift**: Environment-based dependency access for SwiftUI
- **Service Registration**: Repositories, use cases, infrastructure services, and ACS integration
- **Environment Integration**: Seamless dependency injection throughout the SwiftUI view hierarchy

### 🧭 Navigation Architecture
**Specialized Navigation Services:**
- **PageNavigationService**: Main app navigation, routing, and view management
- **TabNavigationService**: Tab-based navigation with state management
- **SheetNavigationService**: Modal presentations, alerts, and sheet management
- **NavigationCoordinator**: Unified coordination layer delegating to specialized services
- **NavigationService**: Legacy compatibility layer with toast notifications

**Navigation Features:**
- Per-tab navigation paths for complex TabView scenarios
- Programmatic navigation with type-safe destinations
- Drawer/sidebar navigation support
- Locale-aware navigation with internationalization
- Comprehensive logging for navigation events

### ✅ Validation System
- **ValidationService**: Comprehensive form validation (email, password, phone, Teams URLs)
- **ValidationUseCaseImpl**: Domain-layer validation use case implementation
- **Real-time Validation**: Email format, password strength, phone number validation
- **Teams Meeting URL Validation**: Specialized validation for Microsoft Teams integration

### 🔗 Azure Communication Services Integration
**Production-Ready ACS Implementation:**
- **ACSService**: Complete ACS SDK integration with real-time calling
- **ACSConfiguration**: Environment-aware configuration management
- **ACSServiceType**: Support for group calls, Teams meetings, and room calls
- **Performance Optimizations**: Memory management, connection tracking, automatic cleanup
- **Error Handling**: Comprehensive error types with user-friendly messages

### 📊 Logging System
**Comprehensive Logging Infrastructure:**
- **12 Specialized Loggers**: UI, Navigation, Auth, Data, Theme, Validation, etc.
- **Structured Logging**: Context-aware logging with user/session information
- **Performance Monitoring**: Built-in performance measurement tools
- **Analytics Integration**: Ready for analytics service integration
- **Debug Utilities**: Debug-only logging with file/line information

### 🎨 Constants Management
**Centralized Configuration:**
- **AppConstants**: Strings, validation rules, network settings, ACS configuration
- **AppDimensions**: Responsive UI dimensions with device scaling
- **ImageConstants**: Centralized image asset management with safe loading
- **Layout Constants**: Standardized spacing, margins, and animation durations

### 🔧 Extensions & Utilities
**String Extensions:**
- Email validation, whitespace handling, date conversion
- Localization support with LocalizedStringKey
- AppStorage key management
- Locale direction support (LTR/RTL)

**Logger Extensions:**
- User interaction tracking
- Navigation event logging
- Performance measurement
- Error context logging
- Feature usage analytics

### 📁 Resource Management
- **Assets.xcassets**: Complete app icon and image asset management
- **ACSConfig.plist**: Azure Communication Services configuration
- **Localizable.xcstrings**: Modern iOS localization system
- **Custom Fonts**: Typography asset management
- **Info.plist**: App permissions and configuration

## 🚀 Run Instructions

### Prerequisites

- **Xcode**: Version 15.0 or higher
- **iOS Deployment Target**: iOS 16.0 or higher
- **Swift**: Version 5.9 or higher
- **macOS**: macOS 13.0 (Ventura) or higher

### Installation & Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd iOSProject
   ```

2. **Open in Xcode**
   ```bash
   open iOSProject.xcodeproj
   ```
   Or double-click the `iOSProject.xcodeproj` file

3. **Select Target Device**
   - Choose iOS Simulator or connected iOS device
   - Ensure deployment target matches your device iOS version

4. **Build and Run**
   - Press `Cmd + R` or click the Run button in Xcode
   - Wait for build to complete and app to launch

### Build Configurations

**Debug Build:**
- Default configuration for development
- Includes debug symbols and logging

**Release Build:**
- Optimized for App Store distribution
- Select "Release" scheme in Xcode

### Testing

**Unit Tests:**
```bash
# Run from command line
xcodebuild test -project iOSProject.xcodeproj -scheme iOSProject -destination 'platform=iOS Simulator,name=iPhone 15'
```

**UI Tests:**
- Run UI tests from Xcode Test Navigator
- Or use `Cmd + U` to run all tests

## ✨ Features and Screenshots

### 🔐 Authentication System
- **Email/Password Login**: Real-time validation with SwiftUI form handling
- **Form Validation**: Email format and password strength validation
- **Animated UI**: Smooth button animations during login process
- **Error Handling**: Comprehensive error states with user feedback

### 🎨 Theme Management
- **Light/Dark Mode**: Complete theme switching with iOS system integration
- **Custom Color System**: Semantic color system with theme-aware components
- **Persistent Preferences**: Theme choice saved with @AppStorage
- **Smooth Transitions**: Animated theme switching across all screens
- **System Integration**: Respects iOS system appearance settings

### 🧭 Navigation & Screens
- **Authentication Flow**: Secure login with navigation coordination
- **Dashboard**: Main app interface with tab-based navigation
- **Profile Management**: User settings and account information
- **Navigation Service**: Centralized navigation management
- **Deep Linking**: URL-based navigation support

### 🏗️ Technical Features
- **Clean Architecture**: Proper separation of concerns with MVVM
- **Dependency Injection**: Custom DI container for service management
- **SwiftUI Best Practices**: Modern SwiftUI patterns and techniques
- **Reactive Programming**: Combine framework integration
- **Error Handling**: Comprehensive error management system
- **Testing Architecture**: Unit and UI testing setup

### 🔧 iOS-Specific Features
- **@AppStorage Integration**: Persistent user preferences
- **Environment Objects**: Shared state management
- **Custom View Modifiers**: Reusable UI components
- **Navigation Coordinator**: Centralized navigation logic
- **Toast Notifications**: Custom toast system for user feedback

*Note: Screenshots would be added here in a real project deployment*

## � Azure Communication Services

This project includes a production-ready integration with Azure Communication Services (ACS) for video calling and Teams meeting functionality.

### Features
- **Teams Meeting Integration**: Join Microsoft Teams meetings directly from the app
- **Video Calling**: High-quality video calls with ACS infrastructure
- **Real-time Status**: Live call state indicators and connection feedback
- **Error Handling**: Comprehensive error management with user-friendly messages
- **Performance Optimized**: Memory management and automatic cleanup

### Setup
For detailed setup instructions, see [ACS Setup Guide](./iOSProject/Documentation/ACS_Setup_Guide.md).

**Quick Setup:**
1. Create an Azure Communication Services resource in Azure Portal
2. Configure credentials in `ACSConfig.plist` or environment variables
3. Build and run the project

### Production Status
✅ **Production Ready** - See [Production Readiness Summary](./iOSProject/Documentation/Production_Readiness_Summary.md) for complete details.

## �🔮 What Would Be Added If More Time

### 🚀 Enhanced Features
- **Biometric Authentication**: Face ID and Touch ID integration
- **Push Notifications**: APNs integration with notification handling
- **Core Data Integration**: Local data persistence with CloudKit sync
- **Advanced Animations**: Complex SwiftUI animations and transitions
- **Widgets**: iOS 14+ widget support for home screen integration

### 🧪 Testing & Quality
- **Comprehensive Test Suite**: Unit tests for all ViewModels and use cases
- **SwiftUI Testing**: UI component testing with ViewInspector
- **Snapshot Testing**: Visual regression testing for UI components
- **Performance Testing**: XCTest performance measurement
- **Code Coverage**: Achieve 90%+ test coverage across all layers

### 🔧 Technical Improvements
- **Real API Integration**: Replace mock data with actual backend services
- **Advanced State Management**: Complex state scenarios with Combine
- **Caching Strategy**: Implement sophisticated data caching with NSCache
- **Security Enhancements**: Keychain integration and certificate pinning
- **Performance Optimization**: Lazy loading and memory optimization

### 🎨 UI/UX Enhancements
- **Advanced Theming**: Dynamic color system with user customization
- **Accessibility**: Complete VoiceOver support and accessibility features
- **iPad Support**: Optimized layouts for iPad with split view support
- **macOS Catalyst**: Mac app support with platform-specific features
- **Design System**: Comprehensive SwiftUI component library

### 🌐 Platform Features
- **Shortcuts Integration**: Siri Shortcuts and iOS automation
- **Handoff Support**: Continuity features across Apple devices
- **Spotlight Integration**: App content searchable in iOS Spotlight
- **Share Extensions**: Native iOS sharing capabilities
- **Background Processing**: Background app refresh and processing

### 📱 iOS Advanced Features
- **ARKit Integration**: Augmented reality features
- **Machine Learning**: Core ML model integration
- **HealthKit Integration**: Health data integration (if applicable)
- **MapKit Features**: Advanced mapping and location services
- **Camera Integration**: Custom camera functionality with AVFoundation

---

## 🤝 Contributing

We welcome contributions to this project! Please follow these guidelines:

### Getting Started
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/your-feature-name`
3. Make your changes following the existing code style
4. Write or update tests as needed
5. Ensure all tests pass
6. Submit a pull request

### Code Style
- Follow Swift API Design Guidelines
- Use SwiftLint for code formatting
- Maintain Clean Architecture principles
- Add documentation for public APIs
- Write meaningful commit messages

### Pull Request Process
1. Update the README.md if needed
2. Ensure your code builds without warnings
3. Add tests for new functionality
4. Update documentation as needed
5. Request review from maintainers

## 🔧 Troubleshooting

### Common Issues

**Build Errors:**
- Ensure Xcode 15.0+ is installed
- Clean build folder: `Product → Clean Build Folder`
- Reset package cache: `File → Packages → Reset Package Caches`

**Azure Communication Services Issues:**
- Verify credentials are properly configured
- Check network connectivity
- Ensure camera/microphone permissions are granted
- See [ACS Setup Guide](./iOSProject/Documentation/ACS_Setup_Guide.md) for detailed troubleshooting

**Theme/UI Issues:**
- Force quit and restart the app
- Check device appearance settings
- Verify iOS version compatibility (16.0+)

**Performance Issues:**
- Monitor memory usage in Xcode Instruments
- Check for retain cycles in ViewModels
- Ensure proper cleanup in deinit methods

### Getting Help
- Check existing [GitHub Issues](https://github.com/your-username/iOSProject/issues)
- Review documentation in `/Documentation/` folder
- Contact support (see Contact section below)

## 📝 Changelog

### Version 1.0.0 (Current)
- ✅ Initial release with Clean Architecture implementation
- ✅ SwiftUI authentication system with form validation
- ✅ Dynamic theme management (light/dark mode)
- ✅ Azure Communication Services integration
- ✅ Production-ready video calling functionality
- ✅ Comprehensive error handling and user feedback
- ✅ Unit and UI testing suite
- ✅ Complete documentation and setup guides

### Upcoming Features
- Biometric authentication (Face ID/Touch ID)
- Push notifications
- Core Data integration
- Advanced animations and transitions

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

```
MIT License

Copyright (c) 2024 iOS SwiftUI Authentication App

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

## 📞 Contact

### Project Maintainer
- **GitHub**: [Your GitHub Profile](https://github.com/your-username)
- **Email**: <EMAIL>

### Support
- **Issues**: [GitHub Issues](https://github.com/your-username/iOSProject/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/iOSProject/discussions)
- **Documentation**: [Project Wiki](https://github.com/your-username/iOSProject/wiki)

### Related Resources
- [Azure Communication Services Documentation](https://docs.microsoft.com/en-us/azure/communication-services/)
- [SwiftUI Documentation](https://developer.apple.com/documentation/swiftui)
- [Clean Architecture Guide](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)

---

**Built using SwiftUI & Clean Architecture with Azure Communication Services**
