import Foundation
import OSLog

// MARK: - Home Repository Protocol
protocol HomeRepositoryProtocol {
    func getHomePageData() async throws -> LandingData
    func getDoctorDashboard() async throws -> DoctorDashboard
}

// MARK: - Real Home Repository Implementation
class RealHomeRepository: HomeRepositoryProtocol {
    
    // MARK: - Properties
    private let remoteDataSource: HomeRemoteDataSourceProtocol
    private let logger = Logger.data
    
    // MARK: - Initialization
    init(remoteDataSource: HomeRemoteDataSourceProtocol) {
        self.remoteDataSource = remoteDataSource
    }
    
    // MARK: - Public Methods
    
    func getHomePageData() async throws -> LandingData {
        logger.info("Repository: Fetching home page data")
        
        do {
            let response = try await remoteDataSource.getHomePageData()
            let landingData = mapToLandingData(response.data)
            
            logger.info("Repository: Successfully mapped home page data")
            return landingData
            
        } catch {
            logger.error("Repository: Failed to get home page data: \(error.localizedDescription)")
            throw mapError(error)
        }
    }
    
    func getDoctorDashboard() async throws -> DoctorDashboard {
        logger.info("Repository: Fetching doctor dashboard data")
        
        do {
            let response = try await remoteDataSource.getDoctorDashboard()
            let dashboard = mapToDoctorDashboard(response.data)
            
            logger.info("Repository: Successfully mapped doctor dashboard data")
            return dashboard
            
        } catch {
            logger.error("Repository: Failed to get doctor dashboard data: \(error.localizedDescription)")
            throw mapError(error)
        }
    }
    
    // MARK: - Private Mapping Methods
    
    private func mapToLandingData(_ data: HomePageData) -> LandingData {
        let features = data.features.map { dto in
            Feature(
                id: dto.id,
                title: dto.title,
                description: dto.description,
                iconName: dto.iconUrl ?? "default_icon",
                isActive: dto.isActive
            )
        }
        
        let pricingPlans = data.pricingPlans.map { dto in
            PricingPlan(
                id: dto.id,
                name: dto.name,
                price: dto.price,
                currency: dto.currency,
                features: dto.features,
                isPopular: dto.isPopular,
                billingPeriod: dto.billingPeriod
            )
        }
        
        return LandingData(
            features: features,
            pricingPlans: pricingPlans
        )
    }
    
    private func mapToDoctorDashboard(_ data: DoctorDashboardData) -> DoctorDashboard {
        let appointments = data.appointments.map { dto in
            Appointment(
                id: dto.id,
                patientName: dto.patientName,
                patientId: dto.patientId,
                dateTime: parseDate(dto.dateTime),
                status: AppointmentStatus(rawValue: dto.status) ?? .pending,
                type: AppointmentType(rawValue: dto.type) ?? .consultation
            )
        }
        
        let patients = data.patients.map { dto in
            Patient(
                id: dto.id,
                name: dto.name,
                email: dto.email,
                phone: dto.phone,
                lastVisit: dto.lastVisit != nil ? parseDate(dto.lastVisit!) : nil
            )
        }
        
        let statistics = DoctorStatistics(
            totalPatients: data.statistics.totalPatients,
            todayAppointments: data.statistics.todayAppointments,
            pendingAppointments: data.statistics.pendingAppointments,
            completedAppointments: data.statistics.completedAppointments
        )
        
        return DoctorDashboard(
            appointments: appointments,
            patients: patients,
            statistics: statistics
        )
    }
    
    private func parseDate(_ dateString: String) -> Date {
        let formatter = ISO8601DateFormatter()
        return formatter.date(from: dateString) ?? Date()
    }
    
    private func mapError(_ error: Error) -> HomeRepositoryError {
        if let dataSourceError = error as? HomeDataSourceError {
            switch dataSourceError {
            case .networkError:
                return .networkError
            case .serverError(let code):
                return .serverError(code)
            case .unauthorized:
                return .unauthorized
            case .parsingError:
                return .dataParsingError
            case .unknownError:
                return .unknownError
            }
        }
        
        return .unknownError
    }
}

// MARK: - Home Repository Error
enum HomeRepositoryError: Error, LocalizedError {
    case networkError
    case serverError(Int)
    case unauthorized
    case dataParsingError
    case unknownError
    
    var errorDescription: String? {
        switch self {
        case .networkError:
            return "Network connection error"
        case .serverError(let code):
            return "Server error (Code: \(code))"
        case .unauthorized:
            return "Unauthorized access"
        case .dataParsingError:
            return "Failed to parse data"
        case .unknownError:
            return "An unknown error occurred"
        }
    }
}

// MARK: - Domain Models

struct DoctorDashboard {
    let appointments: [Appointment]
    let patients: [Patient]
    let statistics: DoctorStatistics
}

struct Appointment {
    let id: String
    let patientName: String
    let patientId: String
    let dateTime: Date
    let status: AppointmentStatus
    let type: AppointmentType
}

enum AppointmentStatus: String, CaseIterable {
    case pending = "pending"
    case confirmed = "confirmed"
    case completed = "completed"
    case cancelled = "cancelled"
}

enum AppointmentType: String, CaseIterable {
    case consultation = "consultation"
    case followUp = "follow_up"
    case emergency = "emergency"
    case checkup = "checkup"
}

struct Patient {
    let id: String
    let name: String
    let email: String
    let phone: String?
    let lastVisit: Date?
}

struct DoctorStatistics {
    let totalPatients: Int
    let todayAppointments: Int
    let pendingAppointments: Int
    let completedAppointments: Int
}

// MARK: - Enhanced Feature Model
extension Feature {
    init(id: String, title: String, description: String, iconName: String, isActive: Bool) {
        self.id = id
        self.title = title
        self.description = description
        self.iconName = iconName
        // Note: isActive property would need to be added to the original Feature model
    }
}

// MARK: - Enhanced PricingPlan Model
extension PricingPlan {
    init(id: String, name: String, price: Double, currency: String, features: [String], isPopular: Bool, billingPeriod: String) {
        self.id = id
        self.name = name
        self.price = price
        self.currency = currency
        self.features = features
        self.isPopular = isPopular
        // Note: billingPeriod property would need to be added to the original PricingPlan model
    }
}
