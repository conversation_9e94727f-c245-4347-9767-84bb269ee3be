import SwiftUI
import Combine
import OSLog
import Foundation

// MARK: - Landing View Model
@MainActor
class HomeViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var isLoading: Bool = false
    @Published var landingData: LandingData = LandingData.default
    @Published var showError: Bool = false
    @Published var errorMessage: String = AppConstants.Strings.genericError
    @Published var newsletterEmail: String = ""
    @Published var isSubscribing: Bool = false
    @Published var showTeamsMeetingAlert: Bool = false
    @Published var teamsMeetingURL: String = ""
    @Published var teamsMeetingURLError: String? = nil

    // MARK: - ACS Loading States
    @Published var isConnectingToCall: Bool = false
    @Published var isRequestingPermissions: Bool = false

    // MARK: - Dependencies
    private let newsUseCase: NewsUseCaseProtocol
    private let acsService: ACSServiceProtocol
    private let navigationService: NavigationServiceProtocol
    private let validationService: ValidationServiceProtocol

    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Computed Properties
    var acsCallState: ACSCallState {
        acsService.callState
    }

    // MARK: - Initialization
    init(newsUseCase: NewsUseCaseProtocol, acsService: ACSServiceProtocol, navigationService: NavigationServiceProtocol, validationService: ValidationServiceProtocol) {
        self.newsUseCase = newsUseCase
        self.acsService = acsService
        self.navigationService = navigationService
        self.validationService = validationService
        loadInitialData()
        setupTeamsURLValidation()
    }

    // MARK: - Setup Methods
    private func setupTeamsURLValidation() {
        // Real-time validation of Teams meeting URL
        $teamsMeetingURL
            .debounce(for: .milliseconds(500), scheduler: RunLoop.main)
            .sink { [weak self] url in
                self?.validateTeamsURLInRealTime(url)
            }
            .store(in: &cancellables)
    }

    private func validateTeamsURLInRealTime(_ url: String) {
        let trimmedURL = url.trimmingCharacters(in: .whitespacesAndNewlines)

        // Clear error if URL is empty (user is still typing)
        if trimmedURL.isEmpty {
            teamsMeetingURLError = nil
            return
        }

        // Validate the URL format
        let validationResult = validationService.validateTeamsMeetingURL(trimmedURL)
        switch validationResult {
        case .valid:
            teamsMeetingURLError = nil
        case .invalid(let message):
            teamsMeetingURLError = message
        }
    }

    // MARK: - Public Methods
    func loadInitialData() {
        Task { @MainActor in
            isLoading = true

            do {
                // Simulate realistic loading delay
                try await Task.sleep(nanoseconds: AppConstants.Delays.medium)

                // Load default data (in a real app, this would come from an API)
                landingData = LandingData.default
                isLoading = false

                Logger.data.info("Home data loaded successfully")
            } catch {
                isLoading = false
                showErrorMessage(AppConstants.Strings.dataLoadErrorWithDetails(error))
                Logger.data.logError(error, context: "Loading home data")
            }
        }
    }

    func refreshData() {
        loadInitialData()
    }

    func appStoreButtonTapped() {
        // Handle App Store button tap
        Logger.ui.logUserInteraction(AppConstants.Strings.appStoreButtonTapped, details: ["screen": "Home"])
    }

    func playStoreButtonTapped() {
        // Handle Play Store button tap
        Logger.ui.logUserInteraction(AppConstants.Strings.playStoreButtonTapped, details: ["screen": "Home"])
    }

    func getStartedTapped() {
        // Handle Get Started button tap
        Logger.ui.logUserInteraction(AppConstants.Strings.getStartedTapped, details: ["screen": "Home"])
    }

    func pricingPlanSelected(_ plan: PricingPlan) {
        // Handle pricing plan selection
        Logger.ui.logUserInteraction(AppConstants.Strings.pricingPlanSelected, details: ["plan": plan.title, "screen": "Home"])
    }

    func subscribeToNewsletter() {
        guard !newsletterEmail.isEmpty else {
            showErrorMessage(AppConstants.Strings.validEmailRequired)
            return
        }

        Task { @MainActor in
            isSubscribing = true

            do {
                // Simulate realistic newsletter subscription delay
                try await Task.sleep(nanoseconds: AppConstants.Delays.newsletter)

                // Reset email field on success
                newsletterEmail = ""
                isSubscribing = false

                // Show success message
                Logger.business.info("\(AppConstants.Strings.newsletterSubscriptionSuccess)")
            } catch {
                isSubscribing = false
                showErrorMessage(AppConstants.Strings.newsletterErrorWithDetails(error))
                Logger.business.logError(error, context: "Newsletter subscription")
            }
        }
    }

    // MARK: - ACS Communication Methods

    func startACSCommunication() {
        // Reset previous state
        teamsMeetingURL = ""
        teamsMeetingURLError = nil
        showTeamsMeetingAlert = true

        // Pre-populate with example if empty
        if teamsMeetingURL.isEmpty {
            // Don't pre-populate to avoid confusion, let user enter their own URL
        }
    }

    func joinTeamsMeetingWithUserInput() {
        let validationResult = validationService.validateTeamsMeetingURL(teamsMeetingURL)

        switch validationResult {
        case .valid:
            teamsMeetingURLError = nil
            showTeamsMeetingAlert = false
            proceedWithTeamsMeeting(url: teamsMeetingURL)
        case .invalid(let message):
            teamsMeetingURLError = message
            showErrorToast("The url you entered is not valid. Please try again.")
        }
    }

    func cancelTeamsMeetingInput() {
        showTeamsMeetingAlert = false
        teamsMeetingURL = ""
        teamsMeetingURLError = nil
    }

    // MARK: - Error Handling Helper
    private func userFriendlyErrorMessage(for error: Error) -> String {
        if let acsError = error as? ACSError {
            switch acsError {
            case .permissionDenied:
                return "Camera and microphone access is required. Please enable in Settings."
            case .networkError:
                return "Connection failed. Please check your internet connection and try again."
            case .callFailed:
                return "Unable to join the call. Please verify the meeting URL and try again."
            case .configurationError:
                return "Configuration error. Please contact support if this persists."
            }
        }
        return error.localizedDescription
    }

    private func proceedWithTeamsMeeting(url: String) {
        Task { @MainActor in
            do {
                await showInitializationToast()
                try await handlePermissions()
                try await connectToCall(url: url)
                await showSuccessAndCleanup()
            } catch {
                await handleCallError(error)
            }
        }
    }

    // MARK: - Private Helper Methods

    private func showInitializationToast() async {
        let infoToast = Toast(
            type: .info,
            title: "Communication",
            message: "Initializing communication session...",
            duration: 3.0
        )
        navigationService.showToast(infoToast)
    }

    private func handlePermissions() async throws {
        isRequestingPermissions = true
        defer { isRequestingPermissions = false }

        let hasPermissions = await acsService.checkPermissions()
        if !hasPermissions {
            try await acsService.requestPermissions()
            await showPermissionGrantedToast()
        }
    }

    private func showPermissionGrantedToast() async {
        let permissionToast = Toast(
            type: .success,
            title: "Permissions",
            message: "Permissions are granted",
            duration: 3.0
        )
        navigationService.showToast(permissionToast)
    }
    
    private func showErrorToast(_ message:String)  {
        let permissionToast = Toast(
            type: .error,
            title: "Error",
            message: message,
            duration: 3.0
        )
        navigationService.showToast(permissionToast)
    }

    private func connectToCall(url: String) async throws {
        isConnectingToCall = true
        defer { isConnectingToCall = false }

        // Sleep for 2 seconds for mocking an api call
        try await Task.sleep(nanoseconds: 2_000_000_000)

        // Create call with user-provided URL
        let groupId = try await acsService.createCall(.teamsMeeting(link: url))

        Logger.ui.logUserInteraction("ACS Communication Started", details: [
            "groupId": groupId ?? "",
            "screen": "Home"
        ])
    }

    private func showSuccessAndCleanup() async {
        navigationService.clearToasts()
    }

    private func handleCallError(_ error: Error) async {
        // Reset loading states
        isRequestingPermissions = false
        isConnectingToCall = false

        // Simple error handling
        let errorToast = Toast(
            type: .error,
            title: "Call Failed",
            message: userFriendlyErrorMessage(for: error),
            duration: 5.0
        )
        navigationService.showToast(errorToast)
        Logger.ui.logError(error, context: "ACS Communication")
    }

    func joinTeamsMeeting(url: String) {
        Task { @MainActor in
            do {
                // Show joining meeting toast
                let joiningToast = Toast(
                    type: .info,
                    title: "Joining Meeting",
                    message: "Connecting to the meeting...",
                    duration: 2.0
                )
                navigationService.showToast(joiningToast)

               let _: String? = try await acsService.joinCall(type: .teamsMeeting(link: url))

                // Show success toast
                let successToast = Toast(
                    type: .success,
                    title: "Call Started",
                    message: "Teams meeting joined successfully",
                    duration: 3.0
                )
                navigationService.showToast(successToast)

                Logger.ui.logUserInteraction("Teams Meeting Joined", details: [
                    "meetingUrl": url,
                    "screen": "Home"
                ])

            } catch {
                // Simple error handling
                let errorToast = Toast(
                    type: .error,
                    title: "Join Failed",
                    message: error.localizedDescription,
                    duration: 5.0
                )
                navigationService.showToast(errorToast)
                Logger.ui.logError(error, context: "Teams Meeting Join")
            }
        }
    }

    // MARK: - Private Methods
    private func showErrorMessage(_ message: String) {
        errorMessage = message
        showError = true
    }
}

// MARK: - Other ViewModels (Placeholder implementations)

// ProfileViewModel moved to Presentation/Profile/ProfileViewModel.swift

// FavouriteViewModel moved to Presentation/Favourites/FavouriteView.swift

// NewsViewModel moved to Presentation/News/NewsView.swift

// NotificationViewModel moved to Presentation/Notifications/NotificationView.swift

// MARK: - Mock Use Cases (temporary implementations)

// MockNewsUseCase moved to Presentation/News/NewsView.swift

class MockUserUseCase: UserUseCaseProtocol {
    func getUser(id: String) async throws -> User {
        try await Task.sleep(nanoseconds: AppConstants.Delays.short)
        return User.mock
    }

    func updateUser(_ user: User) async throws -> User {
        try await Task.sleep(nanoseconds: AppConstants.Delays.long)
        return user
    }

    func getFavourites() async throws -> [String] {
        try await Task.sleep(nanoseconds: AppConstants.Delays.medium)
        return ["1", "2", "3"]
    }

    func toggleFavourite(itemId: String) async throws {
        try await Task.sleep(nanoseconds: AppConstants.Delays.short)
    }
}

// MockNotificationUseCase moved to Presentation/Notifications/NotificationView.swift
