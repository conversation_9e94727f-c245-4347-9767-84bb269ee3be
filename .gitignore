# Xcode
#
# gitignore contributors: remember to update Global/Xcode.gitignore, Objective-C.gitignore & Swift.gitignore

## User settings
xcuserdata/

## compatibility with Xcode 8 and earlier (ignoring not required starting Xcode 9)
*.xcscmblueprint
*.xccheckout

## compatibility with Xcode 3 and earlier (ignoring not required starting Xcode 4)
build/
DerivedData/
*.moved-aside
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3

## Obj-C/Swift specific
*.hmap

## App packaging
*.ipa
*.dSYM.zip
*.dSYM

## Playgrounds
timeline.xctimeline
playground.xcworkspace

# Swift Package Manager
#
# Add this line if you want to avoid checking in source code from Swift Package Manager dependencies.
# Packages/
# Package.pins
# Package.resolved
# *.xcodeproj
#
# Xcode automatically generates this directory with a .xcworkspacedata file and xcuserdata
# hence it is not needed unless you have added a package configuration file to your project
# .swiftpm

.build/

# CocoaPods
#
# We recommend against adding the Pods directory to your .gitignore. However
# you should judge for yourself, the pros and cons are mentioned at:
# https://guides.cocoapods.org/using/using-cocoapods.html#should-i-check-the-pods-directory-into-source-control
#
# Pods/
#
# Add this line if you want to avoid checking in source code from the Xcode workspace
# *.xcworkspace

# Carthage
#
# Add this line if you want to avoid checking in source code from Carthage dependencies.
# Carthage/Checkouts

Carthage/Build/

# Accio dependency management
Dependencies/
.accio/

# fastlane
#
# It is recommended to not store the screenshots in the git repo.
# Instead, use fastlane to re-generate the screenshots whenever they are needed.
# For more information about the recommended setup visit:
# https://docs.fastlane.tools/best-practices/source-control/

fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots/**/*.png
fastlane/test_output

# Code Injection
#
# After new code Injection tools there's a generated folder /iOSInjectionProject
# https://github.com/johnno1962/injectionforxcode

iOSInjectionProject/

# General
.DS_Store
.AppleDouble
.LSOverride

# Icon must end with two \r
Icon

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# IDEs
.vscode/
.idea/

# Backup files
*~.nib
*.swp
*.swo
*~

# OS generated files
Thumbs.db

# Xcode Patch
*.xcodeproj/*
!*.xcodeproj/project.pbxproj
!*.xcodeproj/xcshareddata/
!*.xcworkspace/contents.xcworkspacedata
/*.gcno
**/xcshareddata/WorkspaceSettings.xcsettings

# Swift Package Manager build artifacts
/.build
/Packages
/*.xcodeproj
xcuserdata/
DerivedData/
.swiftpm/xcode/package.xcworkspace/contents.xcworkspacedata

# Azure Communication Services - Security
# Never commit real ACS credentials to version control
.env.acs
.env.acs.*
**/ACSConfig.plist
!**/ACSConfig.template.plist
# Allow the template but not real config files
# CRITICAL: This file contains real ACS credentials and MUST be excluded
iOSProject/Core/Resource/ACSConfig.plist

# Additional iOS Development Files
*.xcuserstate
*.xcuserdatad
project.xcworkspace/xcuserdata/
xcuserdata/

# Provisioning Profiles and Certificates
*.mobileprovision
*.p12
*.cer
*.certSigningRequest

# App Store Connect API Keys
AuthKey_*.p8
private_keys/

# Firebase and other service configuration files
GoogleService-Info.plist
google-services.json
firebase_options.dart

# Environment and configuration files
.env
.env.local
.env.production
.env.staging
config.plist
secrets.plist

# Simulator and device logs
*.crash
*.ips

# Xcode Patch (additional)
*.xcodeproj/project.xcworkspace/xcshareddata/
*.xcodeproj/xcuserdata/
*.xcworkspace/xcuserdata/

# Build artifacts and temporary files
*.app
*.ipa
*.dSYM
*.dSYM.zip
*.xcarchive

# Additional Swift Package Manager
.swiftpm/
Package.resolved
.build/
Packages/

# Additional CocoaPods
Pods/
Podfile.lock

# Additional Carthage
Carthage/
*.framework

# Additional fastlane
fastlane/README.md
fastlane/Fastfile
fastlane/Appfile
fastlane/.env*

# Additional system files
.DS_Store?
ehthumbs.db
Icon?
Thumbs.db

# Additional IDE files
.vscode/settings.json
.idea/
*.swp
*.swo

# Additional backup files
*.orig
*.bak
*.tmp
